<?php

use Adianti\Base\TStandardForm;
use Adianti\Control\TAction;
use Adianti\Core\AdiantiApplicationConfig;
use Adianti\Core\AdiantiCoreApplication;
use Adianti\Database\TTransaction;
use Adianti\Database\TRepository;
use Adianti\Database\TCriteria;
use Adianti\Database\TFilter;
use Adianti\Widget\Form\TEntry;
use Adianti\Widget\Form\TCombo;
use Adianti\Widget\Wrapper\TDBCombo;
use Adianti\Widget\Form\TText;
use Adianti\Widget\Form\TLabel;
use Adianti\Widget\Form\TDate;
use Adianti\Widget\Container\TVBox;
use Adianti\Widget\Container\TPanelGroup;
use Adianti\Widget\Datagrid\TDataGrid;
use Adianti\Widget\Datagrid\TDataGridColumn;
use Adianti\Widget\Dialog\TMessage;
use Adianti\Wrapper\BootstrapDatagridWrapper;
use <PERSON>iant<PERSON>\Wrapper\BootstrapFormBuilder;

class AlunoPacienteForm extends TStandardForm
{
    protected $form;
    protected $presencas_datagrid;
    protected $datagrid_widget;

    public function __construct()
    {
        parent::__construct();

        $ini  = AdiantiApplicationConfig::get();

        $this->setDatabase('educandario');              // defines the database
        $this->setActiveRecord(AlunoPaciente::class);     // defines the active record
        $this->setAfterSaveAction(new TAction(['AlunoPacienteList', 'onReload']));

        $this->form = new BootstrapFormBuilder('form_ALunoPacienteList');
        $this->form->setFormTitle('Cadastro de Aluno/Paciente');
        $this->form->enableClientValidation(); //Validacao html

        // Campos principais
        $id = new TEntry('id');
        $nome = new TEntry('nome');
        $cpf = new TEntry('cpf');
        $data_nasc = new TDate('data_nasc');
        $data_nasc->setMask('dd/mm/yyyy');
        $data_nasc->setDatabaseMask('yyyy-mm-dd'); // ESSENCIAL para salvar corretamente
        $data_ingresso = new TDate('data_ingresso');
        $data_ingresso->setMask('dd/mm/yyyy');
        $data_ingresso->setDatabaseMask('yyyy-mm-dd');
        $data_saida = new TDate('data_saida');
        $data_saida->setMask('dd/mm/yyyy');
        $data_saida->setDatabaseMask('yyyy-mm-dd');
        $tipo_atendimento = new TCombo('tipo_atendimento');
        $cronograma_semanal = new TText('cronograma_semanal');
        $diagnostico = new TText('diagnostico');

        $responsavel_1 = new TEntry('responsavel_1');
        $responsavel_2 = new TEntry('responsavel_2');
        $telefone = new TEntry('telefone');
        $email = new TEntry('email');

        $cidade_id = new TDBCombo('cidade_id', 'educandario', 'Cidade', 'id', 'nome');
        $turma_id = new TDBCombo('turma_id', 'educandario', 'Turma', 'id', 'nome');
        $professora_id = new TDBCombo('professor_id', 'educandario', 'Professor', 'id', 'nome');

        $rua = new TEntry('rua');
        $nro = new TEntry('nro');
        $bairro = new TEntry('bairro');
        $cep = new TEntry('cep');

        $nis_nro = new TEntry('nis_nro');
        $sus_nro = new TEntry('sus_nro');
        $calcado_nro = new TEntry('calcado_nro');
        $roupa_tamanho = new TEntry('roupa_tamanho');
        $presentes_recebiveis = new TText('presentes_recebiveis');
        $alergias = new TText('alergias');
        $medicamentos = new TText('medicamentos');
        $alimentacao = new TText('alimentacao');
        $tipo_transporte = new TEntry('tipo_transporte');
        $nro_pasta = new TEntry('nro_pasta');
        $obs = new TText('obs');

        $tipo_atendimento->addItems(['Aula' => 'Aula', 'Piscina' => 'Piscina']);

        // Tamanhos
        $id->setEditable(FALSE);
        $id->setSize('30%');
        $nome->setSize('100%');
        $tipo_atendimento->setSize('50%');
        $cidade_id->setSize('100%');
        $cpf->setMask('999.999.999-99');
        $telefone->setMask('(99)9.9999.9999');
        //$telefone->addValidation('telefone', new TMinLengthValidator, [15]);

        // Layout do formulário
        $this->form->addFields([new TLabel('Id')], [$id]);
        $this->form->addFields([new TLabel('Nome')], [$nome]);
        $this->form->addFields([new TLabel('CPF')], [$cpf], [new TLabel('NIS')], [$nis_nro], [new TLabel('SUS')], [$sus_nro]);

        // Adicionar ao formulário
        $this->form->addFields(
            [new TLabel('Data Nascimento')],
            [$data_nasc],
            [new TLabel('Data ingresso')],
            [$data_ingresso],
            [new TLabel('Data saída')],
            [$data_saida]
        );
        $this->form->addFields();
        $this->form->addFields([new TLabel('Tipo atendimento')], [$tipo_atendimento], [new TLabel('Turma')], [$turma_id],[new TLabel('Professora')], [$professora_id]);
        $this->form->addFields([new TLabel('Responsável 1')], [$responsavel_1], [new TLabel('Responsável 2')], [$responsavel_2]);
        $this->form->addFields([new TLabel('Telefone')], [$telefone], [new TLabel('Email')], [$email]);
        $this->form->addFields([new TLabel('Rua')], [$rua], [new TLabel('Nº')], [$nro]);
        $this->form->addFields([new TLabel('Bairro')], [$bairro], [new TLabel('CEP')], [$cep]);
        $this->form->addFields([new TLabel('Cidade')], [$cidade_id]);
        $this->form->addFields([new TLabel('Calçado')], [$calcado_nro], [new TLabel('Roupa')], [$roupa_tamanho]);
        $this->form->addFields([new TLabel('Diagnóstico')], [$diagnostico]);
        $this->form->addFields([new TLabel('Cronograma Semanal')], [$cronograma_semanal]);
        $this->form->addFields([new TLabel('Presentes Recebíveis')], [$presentes_recebiveis]);
        $this->form->addFields([new TLabel('Alergias')], [$alergias]);
        $this->form->addFields([new TLabel('Medicamentos')], [$medicamentos]);
        $this->form->addFields([new TLabel('Alimentação')], [$alimentacao]);
        $this->form->addFields([new TLabel('Transporte')], [$tipo_transporte]);
        $this->form->addFields([new TLabel('Nº Pasta')], [$nro_pasta]);
        $this->form->addFields([new TLabel('Observações')], [$obs]);

        $btn = $this->form->addAction('Salvar', new TAction([$this, 'onSave']), 'fa:save');
        $btn->class = 'btn btn-sm btn-primary';
        $this->form->addActionLink(_t('Clear'),  new TAction(array($this, 'onEdit')), 'fa:eraser red');

        $this->form->addHeaderActionLink('Fechar', new TAction([$this, 'onClose']), 'fa:times red');

        // create presencas datagrid
        $this->createPresencasDatagrid();

        // vertical box container
        $container = new TVBox;
        $container->style = 'width: 100%';
        $container->add($this->form);
        $container->add($this->presencas_datagrid);

        parent::add($container);
    }

    /**
     * Create presencas datagrid
     */
    private function createPresencasDatagrid()
    {
        // creates a DataGrid
        $datagrid = new BootstrapDatagridWrapper(new TDataGrid);
        $datagrid->style = 'width: 100%';
        $datagrid->setHeight(200);

        // creates the datagrid columns
        $column_data = new TDataGridColumn('data_chamada', 'Data', 'center', 100);
        $column_terapeuta = new TDataGridColumn('terapeuta->nome', 'Terapeuta', 'left');
        $column_especialidade = new TDataGridColumn('terapeuta->especialidade', 'Especialidade', 'left');
        $column_presente = new TDataGridColumn('presente', 'Presença', 'center', 100);

        // add the columns to the DataGrid
        $datagrid->addColumn($column_data);
        $datagrid->addColumn($column_terapeuta);
        $datagrid->addColumn($column_especialidade);
        $datagrid->addColumn($column_presente);

        // format data column
        $column_data->setTransformer(function($value, $object, $row) {
            return TDate::date2br($value);
        });

        // format presente column
        $column_presente->setTransformer(function($value, $object, $row) {
            if ($value == '1') {
                return '<span class="label label-success">Presente</span>';
            } else {
                return '<span class="label label-danger">Ausente</span>';
            }
        });

        // create the datagrid model
        $datagrid->createModel();

        // wrap in panel
        $panel = new TPanelGroup('Últimas Presenças', 'white');
        $panel->add($datagrid);

        $this->presencas_datagrid = $panel;
        $this->datagrid_widget = $datagrid; // keep reference to actual datagrid
    }

    /**
     * Load presencas for the current aluno
     */
    public function loadPresencas($aluno_id)
    {
        try
        {
            TTransaction::open('educandario');

            $repository = new TRepository('Chamada');
            $criteria = new TCriteria;
            $criteria->add(new TFilter('aluno_id', '=', $aluno_id));
            $criteria->setProperty('order', 'data_chamada');
            $criteria->setProperty('direction', 'desc');
            $criteria->setProperty('limit', 10); // últimas 10 presenças

            $presencas = $repository->load($criteria);

            // Clear and populate datagrid
            if ($this->datagrid_widget)
            {
                $this->datagrid_widget->clear();

                if ($presencas)
                {
                    foreach ($presencas as $presenca)
                    {
                        $this->datagrid_widget->addItem($presenca);
                    }
                }
            }

            TTransaction::close();
        }
        catch (Exception $e)
        {
            new TMessage('error', $e->getMessage());
            TTransaction::rollback();
        }
    }

    /**
     * Override onEdit to load presencas when editing
     */
    public function onEdit($param)
    {
        parent::onEdit($param);

        if (isset($param['id']) && $param['id'])
        {
            $this->loadPresencas($param['id']);
        }
    }

    public static function onClose($param)
    {
        AdiantiCoreApplication::loadPage('AlunoPacienteList', 'onReload');
    }
}
