<?php

/**
 * AlunoPacienteList
 *
 * @version    8.1
 * @package    control
 * @subpackage educandario
 */
class AlunoPacienteList extends TStandardList
{
    protected $form;
    protected $datagrid;
    protected $pageNavigation;
    protected $filter_label;

    public function __construct()
    {
        parent::__construct();

        parent::setDatabase('educandario');
        parent::setActiveRecord('AlunoPaciente');
        parent::setDefaultOrder('id', 'asc');
        parent::addFilterField('id', '=', 'id');
        parent::addFilterField('nome', 'like', 'nome');
        parent::setLimit(TSession::getValue(__CLASS__ . '_limit') ?? 10);
        parent::setAfterSearchCallback([$this, 'onAfterSearch']);

        $this->form = new BootstrapFormBuilder('form_search_AlunoPaciente');
        $this->form->setFormTitle('Alunos/Pacientes');

        $id   = new TEntry('id');
        $nome = new TEntry('nome');
        $turma = new TEntry('turma');
        
        $this->form->addFields([new TLabel('ID')],   [$id]);
        $this->form->addFields([new TLabel('Nome')], [$nome]);
        $this->form->addFields( [new TLabel('Turma')], [$turma]);

        $id->setSize('30%');
        $nome->setSize('100%');

        $this->form->setData(TSession::getValue(__CLASS__ . '_filter_data'));

        $btn = $this->form->addAction(_t('Find'), new TAction([$this, 'onSearch']), 'fa:search');
        $btn->class = 'btn btn-sm btn-primary';

        $this->datagrid = new BootstrapDatagridWrapper(new TDataGrid);
        $this->datagrid->style = 'width: 100%';
        $this->datagrid->setHeight(320);

        // colunas da listagem
        $column_id    = new TDataGridColumn('id', 'ID', 'center', 50);
        $column_nome  = new TDataGridColumn('nome', 'Nome', 'left');
        $column_turma = new TDataGridColumn('turma->nome', 'Turma', 'left');
        $column_cpf   = new TDataGridColumn('cpf', 'CPF', 'left');
        $column_data = new TDataGridColumn('data_nasc', 'Nascimento', 'center');
        $column_data->setTransformer([__CLASS__, 'formatarDataBR']);

        $this->datagrid->addColumn($column_id);
        $this->datagrid->addColumn($column_nome);
        $this->datagrid->addColumn($column_turma);
        $this->datagrid->addColumn($column_cpf);
        $this->datagrid->addColumn($column_data);

        $column_id->setAction(new TAction([$this, 'onReload']), ['order' => 'id']);
        $column_nome->setAction(new TAction([$this, 'onReload']), ['order' => 'nome']);

        // ações
        // create EDIT action
        $action_edit = new TDataGridAction(array('AlunoPacienteForm', 'onEdit'), ['register_state' => 'false']);
        $action_edit->setButtonClass('btn btn-default');
        $action_edit->setLabel(_t('Edit'));
        $action_edit->setImage('far:edit blue');
        $action_edit->setField('id');
        $this->datagrid->addAction($action_edit);

        // create DELETE action
        $action_del = new TDataGridAction([$this, 'onDelete']);
        $action_del->setLabel(_t('Delete'));
        $action_del->setImage('far:trash-alt red');
        $action_del->setField('id');
        $this->datagrid->addAction($action_del);

        $this->datagrid->createModel();

        // create the page navigation
        $this->pageNavigation = new TPageNavigation;
        $this->pageNavigation->enableCounters();
        $this->pageNavigation->setAction(new TAction(array($this, 'onReload')));
        $this->pageNavigation->setWidth($this->datagrid->getWidth());

        // painel
        $panel = new TPanelGroup;
        $panel->add($this->datagrid);
        $panel->addFooter($this->pageNavigation);

        $btnf = TButton::create('find', [$this, 'onSearch'], '', 'fa:search');
        $btnf->style = 'height: 37px; margin-right:4px;';

        $form_search = new TForm('form_search_name');
        $form_search->style = 'float:left;display:flex';
        $form_search->add($nome, true);
        $form_search->add($btnf, true);

        $panel->addHeaderWidget($form_search);

        $panel->addHeaderActionLink('', new TAction(['AlunoPacienteForm', 'onEdit'], ['register_state' => 'false']), 'fa:plus');
        $this->filter_label = $panel->addHeaderActionLink('Filtros', new TAction([$this, 'onShowCurtainFilters']), 'fa:filter');

        // header actions
        $dropdown = new TDropDown(_t('Export'), 'fa:list');
        $dropdown->style = 'height:37px';
        $dropdown->setPullSide('right');
        $dropdown->setButtonClass('btn btn-default waves-effect dropdown-toggle');
        $dropdown->addAction(_t('Save as CSV'), new TAction([$this, 'onExportCSV'], ['register_state' => 'false', 'static' => '1']), 'fa:table fa-fw blue');
        $dropdown->addAction(_t('Save as PDF'), new TAction([$this, 'onExportPDF'], ['register_state' => 'false', 'static' => '1']), 'far:file-pdf fa-fw red');
        $dropdown->addAction(_t('Save as XML'), new TAction([$this, 'onExportXML'], ['register_state' => 'false', 'static' => '1']), 'fa:code fa-fw green');
        $panel->addHeaderWidget($dropdown);

        // header actions
        $dropdown = new TDropDown(TSession::getValue(__CLASS__ . '_limit') ?? '10', '');
        $dropdown->style = 'height:37px';
        $dropdown->setPullSide('right');
        $dropdown->setButtonClass('btn btn-default waves-effect dropdown-toggle');
        $dropdown->addAction(10,   new TAction([$this, 'onChangeLimit'], ['register_state' => 'false', 'static' => '1', 'limit' => '10']));
        $dropdown->addAction(20,   new TAction([$this, 'onChangeLimit'], ['register_state' => 'false', 'static' => '1', 'limit' => '20']));
        $dropdown->addAction(50,   new TAction([$this, 'onChangeLimit'], ['register_state' => 'false', 'static' => '1', 'limit' => '50']));
        $dropdown->addAction(100,  new TAction([$this, 'onChangeLimit'], ['register_state' => 'false', 'static' => '1', 'limit' => '100']));
        $dropdown->addAction(1000, new TAction([$this, 'onChangeLimit'], ['register_state' => 'false', 'static' => '1', 'limit' => '1000']));
        $panel->addHeaderWidget($dropdown);

        if (TSession::getValue(get_class($this) . '_filter_counter') > 0) {
            $this->filter_label->class = 'btn btn-primary';
            $this->filter_label->setLabel('Filtros (' . TSession::getValue(get_class($this) . '_filter_counter') . ')');
        }

        // vertical box container
        $container = new TVBox;
        $container->style = 'width: 100%';
        //$container->add(new TXMLBreadCrumb('menu.xml', __CLASS__));
        //$container->add($this->form);
        $container->add($panel);

        parent::add($container);
    }

    public function onAfterSearch($datagrid, $options)
    {
        if (TSession::getValue(get_class($this) . '_filter_counter') > 0) {
            $this->filter_label->class = 'btn btn-primary';
            $this->filter_label->setLabel('Filtros (' . TSession::getValue(get_class($this) . '_filter_counter') . ')');
        } else {
            $this->filter_label->class = 'btn btn-default';
            $this->filter_label->setLabel('Filtros');
        }

        if (!empty(TSession::getValue(get_class($this) . '_filter_data'))) {
            $obj = new stdClass;
            $obj->nome = TSession::getValue(get_class($this) . '_filter_data')->nome;
            TForm::sendData('form_search_name', $obj);
        }
    }

    /**
     *
     */
    public static function onChangeLimit($param)
    {
        TSession::setValue(__CLASS__ . '_limit', $param['limit']);
        AdiantiCoreApplication::loadPage(__CLASS__, 'onReload');
    }

    /**
     *
     */
    public static function onShowCurtainFilters($param = null)
    {
        try {
            // create empty page for right panel
            $page = new TPage;
            $page->setTargetContainer('adianti_right_panel');
            $page->setProperty('override', 'true');
            $page->setPageName(__CLASS__);

            $btn_close = new TButton('closeCurtain');
            $btn_close->onClick = "Template.closeRightPanel();";
            $btn_close->setLabel("Fechar");
            $btn_close->setImage('fas:times');

            // instantiate self class, populate filters in construct 
            $embed = new self;
            $embed->form->addHeaderWidget($btn_close);

            // embed form inside curtain
            $page->add($embed->form);
            $page->setIsWrapped(true);
            $page->show();
        } catch (Exception $e) {
            new TMessage('error', $e->getMessage());
        }
    }
    public static function formatarDataBR($value)
    {
        if (!empty($value) && $value != '0000-00-00') {
            $date = new DateTime($value);
            return $date->format('d/m/Y');
        }
        return '';
    }
}
