<?php

use Adianti\Base\TStandardForm;
use Adianti\Control\TAction;
use Adianti\Core\AdiantiApplicationConfig;
use Adianti\Core\AdiantiCoreApplication;
use Adianti\Widget\Form\TEntry;
use Adianti\Widget\Form\TCombo;
use Adianti\Widget\Form\TText;
use Adianti\Widget\Form\TLabel;
use Adianti\Widget\Form\TDate;
use Adianti\Widget\Container\TVBox;
use Adianti\Wrapper\BootstrapFormBuilder;

class DoadorForm extends TStandardForm
{
    protected $form;

    public function __construct()
    {
        parent::__construct();

        $ini  = AdiantiApplicationConfig::get();

        $this->setDatabase('educandario');
        $this->setActiveRecord(Doador::class);
        $this->setAfterSaveAction(new TAction(['DoadorList', 'onReload']));

        $this->form = new BootstrapFormBuilder('form_DoadorForm');
        $this->form->setFormTitle('Cadastro de Doador');
        $this->form->enableClientValidation();

        // Campos principais
        $id = new TEntry('id');
        $numero_cadastro = new TEntry('numero_cadastro');
        $nome = new TEntry('nome');
        $telefone = new TEntry('telefone');
        $telefone2 = new TEntry('telefone2');
        $endereco = new TEntry('endereco');
        $cpf = new TEntry('cpf');
        $cnpj = new TEntry('cnpj');
        $tipo_doacao = new TCombo('tipo_doacao');
        $data_aniversario = new TDate('data_aniversario');
        $obs = new TText('obs');
        $email = new TEntry('email');
        $ativo = new TCombo('ativo');

        // Configurações dos campos
        $id->setEditable(FALSE);
        $id->setSize('30%');
        $nome->setSize('100%');
        $endereco->setSize('100%');
        $cpf->setMask('999.999.999-99');
        $cnpj->setMask('99.999.999/9999-99');
        $telefone->setMask('(99)9.9999.9999');
        $telefone2->setMask('(99)9.9999.9999');
        $data_aniversario->setMask('dd/mm/yyyy');
        $data_aniversario->setDatabaseMask('yyyy-mm-dd');

        // Opções dos combos
        $tipo_doacao->addItems([
            'material' => 'Material',
            'alimento' => 'Alimento', 
            'dinheiro' => 'Dinheiro',
            'funcionaria' => 'Funcionária'
        ]);

        $ativo->addItems([
            '1' => 'Ativo',
            '0' => 'Inativo'
        ]);

        // Campos obrigatórios
        $nome->addValidation('Nome', new TRequiredValidator);

        // Layout do formulário
        $this->form->addFields([new TLabel('Id')], [$id]);
        $this->form->addFields([new TLabel('Número Cadastro')], [$numero_cadastro]);
        $this->form->addFields([new TLabel('Nome *')], [$nome]);
        $this->form->addFields([new TLabel('CPF')], [$cpf], [new TLabel('CNPJ')], [$cnpj]);
        $this->form->addFields([new TLabel('Telefone')], [$telefone], [new TLabel('Telefone 2')], [$telefone2]);
        $this->form->addFields([new TLabel('Endereço')], [$endereco]);
        $this->form->addFields([new TLabel('Email')], [$email]);
        $this->form->addFields([new TLabel('Tipo Doação')], [$tipo_doacao], [new TLabel('Status')], [$ativo]);
        $this->form->addFields([new TLabel('Data Aniversário')], [$data_aniversario]);
        $this->form->addFields([new TLabel('Observações')], [$obs]);

        $btn = $this->form->addAction('Salvar', new TAction([$this, 'onSave']), 'fa:save');
        $btn->class = 'btn btn-sm btn-primary';
        $this->form->addActionLink(_t('Clear'),  new TAction(array($this, 'onEdit')), 'fa:eraser red');

        $this->form->addHeaderActionLink('Fechar', new TAction([$this, 'onClose']), 'fa:times red');

        // vertical box container
        $container = new TVBox;
        $container->style = 'width: 100%';
        $container->add($this->form);

        parent::add($container);
    }

    public static function onClose($param)
    {
        AdiantiCoreApplication::loadPage('DoadorList', 'onReload');
    }
}
