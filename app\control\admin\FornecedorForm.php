<?php

use Adianti\Base\TStandardForm;
use Adianti\Control\TAction;
use Adianti\Core\AdiantiApplicationConfig;
use Adianti\Core\AdiantiCoreApplication;
use Adianti\Widget\Form\TEntry;
use Adianti\Widget\Form\TCombo;
use Adianti\Widget\Form\TText;
use Adianti\Widget\Form\TLabel;
use Adianti\Widget\Form\TPassword;
use Adianti\Widget\Container\TVBox;
use Adianti\Wrapper\BootstrapFormBuilder;

class FornecedorForm extends TStandardForm
{
    protected $form;

    public function __construct()
    {
        parent::__construct();

        $ini  = AdiantiApplicationConfig::get();

        $this->setDatabase('educandario');
        $this->setActiveRecord(Fornecedor::class);
        $this->setAfterSaveAction(new TAction(['FornecedorList', 'onReload']));

        $this->form = new BootstrapFormBuilder('form_FornecedorForm');
        $this->form->setFormTitle('Cadastro de Fornecedor');
        $this->form->enableClientValidation();

        // Campos principais
        $id = new TEntry('id');
        $razao_social = new TEntry('razao_social');
        $nome_fantasia = new TEntry('nome_fantasia');
        $endereco = new TEntry('endereco');
        $telefone = new TEntry('telefone');
        $cnpj = new TEntry('cnpj');
        $forma_pagamento = new TEntry('forma_pagamento');
        $produto = new TEntry('produto');
        $contato = new TEntry('contato');
        $email = new TEntry('email');
        $site = new TEntry('site');
        $senha = new TPassword('senha');
        $ativo = new TCombo('ativo');

        // Configurações dos campos
        $id->setEditable(FALSE);
        $id->setSize('30%');
        $razao_social->setSize('100%');
        $nome_fantasia->setSize('100%');
        $endereco->setSize('100%');
        $cnpj->setMask('99.999.999/9999-99');
        $telefone->setMask('(99)9.9999.9999');

        // Opções dos combos
        $ativo->addItems([
            '1' => 'Ativo',
            '0' => 'Inativo'
        ]);

        // Campos obrigatórios
        $razao_social->addValidation('Razão Social', new TRequiredValidator);

        // Layout do formulário
        $this->form->addFields([new TLabel('Id')], [$id]);
        $this->form->addFields([new TLabel('Razão Social *')], [$razao_social]);
        $this->form->addFields([new TLabel('Nome Fantasia')], [$nome_fantasia]);
        $this->form->addFields([new TLabel('CNPJ')], [$cnpj], [new TLabel('Telefone')], [$telefone]);
        $this->form->addFields([new TLabel('Endereço')], [$endereco]);
        $this->form->addFields([new TLabel('Email')], [$email], [new TLabel('Site')], [$site]);
        $this->form->addFields([new TLabel('Contato')], [$contato], [new TLabel('Status')], [$ativo]);
        $this->form->addFields([new TLabel('Forma Pagamento')], [$forma_pagamento]);
        $this->form->addFields([new TLabel('Produto')], [$produto]);
        $this->form->addFields([new TLabel('Senha')], [$senha]);

        $btn = $this->form->addAction('Salvar', new TAction([$this, 'onSave']), 'fa:save');
        $btn->class = 'btn btn-sm btn-primary';
        $this->form->addActionLink(_t('Clear'),  new TAction(array($this, 'onEdit')), 'fa:eraser red');

        $this->form->addHeaderActionLink('Fechar', new TAction([$this, 'onClose']), 'fa:times red');

        // vertical box container
        $container = new TVBox;
        $container->style = 'width: 100%';
        $container->add($this->form);

        parent::add($container);
    }

    public static function onClose($param)
    {
        AdiantiCoreApplication::loadPage('FornecedorList', 'onReload');
    }
}
