<?php

use Adianti\Base\TStandardForm;
use Adianti\Control\TAction;
use Adianti\Core\AdiantiApplicationConfig;
use Adianti\Core\AdiantiCoreApplication;
use Adianti\Widget\Form\TEntry;
use Adianti\Widget\Form\TCombo;
use Adianti\Widget\Form\TText;
use Adianti\Widget\Form\TLabel;
use Adianti\Widget\Form\TDate;
use Adianti\Widget\Container\TVBox;
use Adianti\Wrapper\BootstrapFormBuilder;

class VoluntarioForm extends TStandardForm
{
    protected $form;

    public function __construct()
    {
        parent::__construct();

        $ini  = AdiantiApplicationConfig::get();

        $this->setDatabase('educandario');
        $this->setActiveRecord(Voluntario::class);
        $this->setAfterSaveAction(new TAction(['VoluntarioList', 'onReload']));

        $this->form = new BootstrapFormBuilder('form_VoluntarioForm');
        $this->form->setFormTitle('Cadastro de Voluntário');
        $this->form->enableClientValidation();

        // Campos principais
        $id = new TEntry('id');
        $nome = new TEntry('nome');
        $endereco = new TEntry('endereco');
        $telefone = new TEntry('telefone');
        $data_nascimento = new TDate('data_nascimento');
        $data_cadastro = new TDate('data_cadastro');
        $ativo = new TCombo('ativo');
        $atividades_executadas = new TText('atividades_executadas');
        $horario_semanal = new TText('horario_semanal');
        $email = new TEntry('email');
        $qtd_horas_semanais = new TEntry('qtd_horas_semanais');

        // Configurações dos campos
        $id->setEditable(FALSE);
        $id->setSize('30%');
        $nome->setSize('100%');
        $endereco->setSize('100%');
        $telefone->setMask('(99)9.9999.9999');
        $data_nascimento->setMask('dd/mm/yyyy');
        $data_nascimento->setDatabaseMask('yyyy-mm-dd');
        $data_cadastro->setMask('dd/mm/yyyy');
        $data_cadastro->setDatabaseMask('yyyy-mm-dd');
        $qtd_horas_semanais->setSize('30%');

        // Opções dos combos
        $ativo->addItems([
            '1' => 'Ativo',
            '0' => 'Inativo'
        ]);

        // Campos obrigatórios
        $nome->addValidation('Nome', new TRequiredValidator);

        // Layout do formulário
        $this->form->addFields([new TLabel('Id')], [$id]);
        $this->form->addFields([new TLabel('Nome *')], [$nome]);
        $this->form->addFields([new TLabel('Endereço')], [$endereco]);
        $this->form->addFields([new TLabel('Telefone')], [$telefone], [new TLabel('Email')], [$email]);
        $this->form->addFields([new TLabel('Data Nascimento')], [$data_nascimento], [new TLabel('Data Cadastro')], [$data_cadastro]);
        $this->form->addFields([new TLabel('Status')], [$ativo], [new TLabel('Qtd Horas Semanais')], [$qtd_horas_semanais]);
        $this->form->addFields([new TLabel('Atividades Executadas')], [$atividades_executadas]);
        $this->form->addFields([new TLabel('Horário Semanal')], [$horario_semanal]);

        $btn = $this->form->addAction('Salvar', new TAction([$this, 'onSave']), 'fa:save');
        $btn->class = 'btn btn-sm btn-primary';
        $this->form->addActionLink(_t('Clear'),  new TAction(array($this, 'onEdit')), 'fa:eraser red');

        $this->form->addHeaderActionLink('Fechar', new TAction([$this, 'onClose']), 'fa:times red');

        // vertical box container
        $container = new TVBox;
        $container->style = 'width: 100%';
        $container->add($this->form);

        parent::add($container);
    }

    public static function onClose($param)
    {
        AdiantiCoreApplication::loadPage('VoluntarioList', 'onReload');
    }
}
