<?php

use Adianti\Database\TRecord;

/**
 * AlunoPaciente
 *
 * @version    8.1
 * @package    model
 * @subpackage educandario
 */
class AlunoPaciente extends TRecord
{
    const TABLENAME  = 'alunos_pacientes';
    const PRIMARYKEY = 'id';

    private $turma;

    /**
     * Constructor method
     */
    public function __construct($id = NULL)
    {
        parent::__construct($id);

        parent::addAttribute('nome');
        parent::addAttribute('data_nasc');
        parent::addAttribute('cpf');
        parent::addAttribute('data_ingresso');
        parent::addAttribute('data_saida');
        parent::addAttribute('tipo_atendimento_id');
        parent::addAttribute('cronograma_semanal');
        parent::addAttribute('nro_pasta');
        parent::addAttribute('diagnostico');
        parent::addAttribute('rua');
        parent::addAttribute('nro');
        parent::addAttribute('bairro');
        parent::addAttribute('cidade_id');
        parent::addAttribute('cep');
        parent::addAttribute('telefone');
        parent::addAttribute('email');
        parent::addAttribute('nis_nro');
        parent::addAttribute('sus_nro');
        parent::addAttribute('calcado_nro');
        parent::addAttribute('roupa_tamanho');
        parent::addAttribute('presentes_recebiveis');
        parent::addAttribute('alergias');
        parent::addAttribute('medicamentos');
        parent::addAttribute('alimentacao');
        parent::addAttribute('transporte_id');
        parent::addAttribute('turma_id');
        parent::addAttribute('professor_id');
        parent::addAttribute('obs');
    }

    public function get_turma()
    {
        if (empty($this->turma)) {
            $this->turma = new Turma($this->turma_id);
        }
        return $this->turma;
    }
}
