<?php

use Adianti\Database\TRecord;

/**
 * Doador
 *
 * @version    8.1
 * @package    model
 * @subpackage educandario
 */
class Doador extends TRecord
{
    const TABLENAME  = 'doadores';
    const PRIMARYKEY = 'id';

    /**
     * Constructor method
     */
    public function __construct($id = NULL)
    {
        parent::__construct($id);

        parent::addAttribute('numero_cadastro');
        parent::addAttribute('nome');
        parent::addAttribute('telefone');
        parent::addAttribute('telefone2');
        parent::addAttribute('endereco');
        parent::addAttribute('cpf');
        parent::addAttribute('cnpj');
        parent::addAttribute('tipo_doacao');
        parent::addAttribute('data_aniversario');
        parent::addAttribute('obs');
        parent::addAttribute('email');
        parent::addAttribute('data_cadastro');
        parent::addAttribute('ativo');
    }
}
