<?php

use Adianti\Database\TRecord;

/**
 * Fornecedor
 *
 * @version    8.1
 * @package    model
 * @subpackage educandario
 */
class Fornecedor extends TRecord
{
    const TABLENAME  = 'fornecedores';
    const PRIMARYKEY = 'id';

    /**
     * Constructor method
     */
    public function __construct($id = NULL)
    {
        parent::__construct($id);

        parent::addAttribute('razao_social');
        parent::addAttribute('nome_fantasia');
        parent::addAttribute('endereco');
        parent::addAttribute('telefone');
        parent::addAttribute('cnpj');
        parent::addAttribute('forma_pagamento');
        parent::addAttribute('produto');
        parent::addAttribute('contato');
        parent::addAttribute('email');
        parent::addAttribute('site');
        parent::addAttribute('senha');
        parent::addAttribute('data_cadastro');
        parent::addAttribute('ativo');
    }
}
