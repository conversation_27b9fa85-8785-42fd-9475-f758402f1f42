<?php

use Adianti\Database\TRecord;

/**
 * Voluntario
 *
 * @version    8.1
 * @package    model
 * @subpackage educandario
 */
class Voluntario extends TRecord
{
    const TABLENAME  = 'voluntarios';
    const PRIMARYKEY = 'id';

    /**
     * Constructor method
     */
    public function __construct($id = NULL)
    {
        parent::__construct($id);

        parent::addAttribute('nome');
        parent::addAttribute('endereco');
        parent::addAttribute('telefone');
        parent::addAttribute('data_nascimento');
        parent::addAttribute('data_cadastro');
        parent::addAttribute('ativo');
        parent::addAttribute('atividades_executadas');
        parent::addAttribute('horario_semanal');
        parent::addAttribute('email');
        parent::addAttribute('qtd_horas_semanais');
    }
}
